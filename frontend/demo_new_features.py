#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示新增功能的启动脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import gradio_app_v2

def main():
    """启动演示界面"""
    print("🚀 启动新增功能演示界面...")
    print("=" * 60)
    print("📋 新增功能列表:")
    print("   1. 🚗 汽车知识库问答 - 基于汽车领域知识的专业问答")
    print("   2. 🌐 全库问答 - 基于所有知识库的综合问答")
    print("   3. 🔍 检索功能 - 纯检索功能，只返回相关文档")
    print("=" * 60)
    print("🔧 API接口对应关系:")
    print("   - 汽车知识库: /rag-qa (knowledge_type='car')")
    print("   - 全库问答: /all-qa")
    print("   - 检索功能: /search")
    print("=" * 60)
    print("✨ 功能特点:")
    print("   - 流式输出支持")
    print("   - 独立的对话历史管理")
    print("   - 参考硬工知识库问答的实现方式")
    print("   - 响应式界面设计")
    print("   - 实时计时显示")
    print("=" * 60)
    
    try:
        # 创建界面
        interface = gradio_app_v2.create_gradio_interface()
        print("✅ 界面创建成功!")
        
        # 启动界面
        print("🌐 启动Web界面...")
        print("📱 界面将在浏览器中打开")
        print("🔗 本地访问地址: http://localhost:7860")
        print("💡 提示: 使用 Ctrl+C 停止服务")
        print("=" * 60)
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=True,
            debug=True,
            show_error=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在关闭服务...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("✅ 服务已关闭")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
