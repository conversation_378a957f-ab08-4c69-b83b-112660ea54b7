#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的前端模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import gradio_app_v2

def test_interface_creation():
    """测试界面创建"""
    try:
        interface = gradio_app_v2.create_gradio_interface()
        print("✅ 界面创建成功!")
        return True
    except Exception as e:
        print(f"❌ 界面创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_app_initialization():
    """测试ChatApp初始化"""
    try:
        app = gradio_app_v2.ChatApp()
        
        # 检查所有对话历史是否初始化
        assert hasattr(app, 'llm_conversation_history'), "缺少LLM对话历史"
        assert hasattr(app, 'rag_conversation_history'), "缺少RAG对话历史"
        assert hasattr(app, 'dataqa_conversation_history'), "缺少DATAQA对话历史"
        assert hasattr(app, 'car_conversation_history'), "缺少汽车知识库对话历史"
        assert hasattr(app, 'all_conversation_history'), "缺少ALL问答对话历史"
        
        print("✅ ChatApp初始化成功!")
        print(f"   - LLM对话历史: {len(app.llm_conversation_history)} 条")
        print(f"   - RAG对话历史: {len(app.rag_conversation_history)} 条")
        print(f"   - DATAQA对话历史: {len(app.dataqa_conversation_history)} 条")
        print(f"   - 汽车知识库对话历史: {len(app.car_conversation_history)} 条")
        print(f"   - ALL问答对话历史: {len(app.all_conversation_history)} 条")
        
        return True
    except Exception as e:
        print(f"❌ ChatApp初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clear_history():
    """测试清空历史功能"""
    try:
        app = gradio_app_v2.ChatApp()
        
        # 添加一些测试数据
        app.llm_conversation_history.append({"query": "test", "content": "test"})
        app.rag_conversation_history.append({"query": "test", "content": "test"})
        app.dataqa_conversation_history.append({"query": "test", "content": "test"})
        app.car_conversation_history.append({"query": "test", "content": "test"})
        app.all_conversation_history.append({"query": "test", "content": "test"})
        
        # 测试清空所有历史
        app.clear_history("all")
        assert len(app.llm_conversation_history) == 0, "LLM历史未清空"
        assert len(app.rag_conversation_history) == 0, "RAG历史未清空"
        assert len(app.dataqa_conversation_history) == 0, "DATAQA历史未清空"
        assert len(app.car_conversation_history) == 0, "汽车知识库历史未清空"
        assert len(app.all_conversation_history) == 0, "ALL问答历史未清空"
        
        print("✅ 清空历史功能测试成功!")
        return True
    except Exception as e:
        print(f"❌ 清空历史功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新增的前端模块...")
    print("=" * 50)
    
    tests = [
        ("界面创建测试", test_interface_creation),
        ("ChatApp初始化测试", test_chat_app_initialization),
        ("清空历史功能测试", test_clear_history),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        print("\n📝 新增功能总结:")
        print("   1. ✅ 汽车知识库问答模块 (🚗 汽车知识库)")
        print("   2. ✅ 全库问答模块 (🌐 全库问答)")
        print("   3. ✅ 检索模块 (🔍 检索)")
        print("   4. ✅ 对应的API接口集成")
        print("   5. ✅ 流式输出支持")
        print("   6. ✅ 历史对话管理")
        print("   7. ✅ 界面布局和样式")
    else:
        print("❌ 部分测试失败，请检查代码")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
